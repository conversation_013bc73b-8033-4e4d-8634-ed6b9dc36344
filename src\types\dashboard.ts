export interface CardPosition {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  visible: boolean;
}

export interface DashboardLayout {
  cards: Record<string, CardPosition>;
  gridLayout: string[];
}

export interface CardConfig {
  id: string;
  title: string;
  description: string;
  defaultVisible: boolean;
  category: 'field' | 'weather' | 'analytics' | 'recommendations' | 'controls';
}

export const CARD_CONFIGS: Record<string, CardConfig> = {
  'wheat-field': {
    id: 'wheat-field',
    title: 'Wheat Field',
    description: 'Wheat field monitoring and management',
    defaultVisible: true,
    category: 'field'
  },
  'left-field': {
    id: 'left-field',
    title: 'Corn Field',
    description: 'Corn field information and controls',
    defaultVisible: true,
    category: 'field'
  },
  'soybean-field': {
    id: 'soybean-field',
    title: 'Soybean Field',
    description: 'Soybean field monitoring and management',
    defaultVisible: true,
    category: 'field'
  },
  'right-field': {
    id: 'right-field',
    title: 'Empty Field',
    description: 'Empty field information and controls',
    defaultVisible: true,
    category: 'field'
  },
  'weather': {
    id: 'weather',
    title: 'Weather',
    description: 'Current weather conditions',
    defaultVisible: true,
    category: 'weather'
  },
  'status-cards': {
    id: 'status-cards',
    title: 'Field Status',
    description: 'Plant health, water depth, and soil conditions',
    defaultVisible: true,
    category: 'analytics'
  },
  'recommendations': {
    id: 'recommendations',
    title: 'Recommendations',
    description: 'AI-powered farming recommendations',
    defaultVisible: true,
    category: 'recommendations'
  },
  'map': {
    id: 'map',
    title: 'Field Map',
    description: 'Interactive field mapping',
    defaultVisible: true,
    category: 'analytics'
  },
  'crop-selector': {
    id: 'crop-selector',
    title: 'Crop Controls',
    description: 'Crop selection and rate settings',
    defaultVisible: true,
    category: 'controls'
  },
  'growth-chart': {
    id: 'growth-chart',
    title: 'Growth Rate',
    description: 'Growth rate analytics and trends',
    defaultVisible: true,
    category: 'analytics'
  },
  'weather-metrics': {
    id: 'weather-metrics',
    title: 'Weather Metrics',
    description: 'Temperature, radiation, VPD, and evapotranspiration data',
    defaultVisible: true,
    category: 'weather'
  },
  'weather-forecast': {
    id: 'weather-forecast',
    title: '7-Day Forecast',
    description: 'Extended weather forecast with detailed metrics',
    defaultVisible: true,
    category: 'weather'
  }
};
