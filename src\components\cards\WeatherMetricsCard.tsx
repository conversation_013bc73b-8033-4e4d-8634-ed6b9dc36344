'use client';

import React from 'react';

interface MetricCardProps {
  title: string;
  value: string;
  unit: string;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: React.ReactNode;
  color: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, unit, change, changeType, icon, color }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm p-4 border border-gray-100 h-full">
      <div className="flex items-center gap-2 mb-3">
        <div className={`w-3 h-3 rounded-full ${color}`}></div>
        <span className="text-sm font-semibold text-gray-800">{title}</span>
      </div>

      <div className="mb-3">
        <div className="text-sm font-medium text-gray-900 leading-tight">{value}</div>
        {unit && <div className="text-xs text-gray-500 mt-1">{unit}</div>}
      </div>

      <div className="flex items-center gap-1 text-xs text-gray-500">
        <svg
          width="12"
          height="12"
          viewBox="0 0 24 24"
          fill="none"
          className={changeType === 'decrease' ? 'rotate-180' : ''}
        >
          <path
            d="M7 14L12 9L17 14"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        <span>{change}</span>
      </div>
    </div>
  );
};

export const WeatherMetricsCard: React.FC = () => {
  const metrics = [
    {
      title: 'Temperature',
      value: 'H: 22°C L: 20°C Avg: 21°C',
      unit: '',
      change: '1°C lower than yesterday',
      changeType: 'decrease' as const,
      icon: null,
      color: 'bg-blue-500'
    },
    {
      title: 'Accumulated Radiation',
      value: 'Daily accumulation: 1526 J/cm2',
      unit: '',
      change: '177J/cm2 lower than yesterday',
      changeType: 'decrease' as const,
      icon: null,
      color: 'bg-red-500'
    },
    {
      title: 'Vapor Pressure Deficit (VPD)',
      value: 'Highest at 3:00 AM - 6:00 AM',
      unit: '',
      change: '53% lower than yesterday',
      changeType: 'decrease' as const,
      icon: null,
      color: 'bg-purple-500'
    },
    {
      title: 'Evapotranspiration (ETo)',
      value: 'Highest at 2:00 PM - 3:00 PM',
      unit: '',
      change: '0.06mm higher than yesterday',
      changeType: 'increase' as const,
      icon: null,
      color: 'bg-green-500'
    }
  ];

  return (
    <div className="grid grid-cols-4 gap-4">
      {metrics.map((metric, index) => (
        <MetricCard key={index} {...metric} />
      ))}
    </div>
  );
};
