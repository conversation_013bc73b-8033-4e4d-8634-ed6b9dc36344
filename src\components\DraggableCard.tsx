'use client';

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface DraggableCardProps {
  id: string;
  children: React.ReactNode;
  className?: string;
  isDragDisabled?: boolean;
}

export const DraggableCard: React.FC<DraggableCardProps> = ({
  id,
  children,
  className = '',
  isDragDisabled = false
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ 
    id,
    disabled: isDragDisabled
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`${className} ${isDragging ? 'z-50' : ''} ${!isDragDisabled ? 'cursor-grab active:cursor-grabbing' : ''}`}
      {...attributes}
      {...listeners}
    >
      {/* Drag handle indicator */}
      {!isDragDisabled && (
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
          <div className="bg-gray-600 text-white rounded p-1 text-xs">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
              <path d="M8 6H16M8 12H16M8 18H16" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </div>
        </div>
      )}
      
      {/* Card content */}
      <div className="relative group">
        {children}
      </div>
    </div>
  );
};
