import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "@/styles/globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Mi App Next.js",
  description: "Aplicación Next.js con TypeScript y Tailwind",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es">
      <body className={`antialiased ${inter.className}`}>
        {children}
      </body>
    </html>
  );
}
