'use client';

import React from 'react';
import { ComposedChart, Bar, Line, XAxis, YAxis, ResponsiveContainer, ReferenceLine } from 'recharts';
import LocationOnIcon from '@mui/icons-material/LocationOn';

// Sample data for the weather forecast chart - matching the image design
const weatherData = [
  { time: '12:00 AM', temp: 20, precipitation: 0, humidity: 240 },
  { time: '2:00 AM', temp: 20, precipitation: 0, humidity: 240 },
  { time: '4:00 AM', temp: 24, precipitation: 0, humidity: 180 },
  { time: '6:00 AM', temp: 24, precipitation: 2, humidity: 180 },
  { time: '8:00 AM', temp: 22, precipitation: 2, humidity: 180 },
  { time: '10:00 AM', temp: 22, precipitation: 8, humidity: 200 },
  { time: '12:00 PM', temp: 23, precipitation: 14, humidity: 220 },
  { time: '2:00 PM', temp: 24, precipitation: 22, humidity: 240 },
  { time: '4:00 PM', temp: 24, precipitation: 24, humidity: 240 },
  { time: '6:00 PM', temp: 22, precipitation: 20, humidity: 220 },
  { time: '8:00 PM', temp: 18, precipitation: 16, humidity: 180 },
  { time: '10:00 PM', temp: 15, precipitation: 4, humidity: 120 },
  { time: '12:00 AM', temp: 12, precipitation: 0, humidity: 120 },
];

export const WeatherCard: React.FC = () => {
  return (
    <div className="bg-white rounded-2xl shadow-lg p-4 flex flex-col border border-gray-100 h-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">Block 22 Forecast, Wed October 23</h3>
      </div>

      {/* Main Chart */}
      <div className="h-64 mb-4">
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart data={weatherData} margin={{ top: 20, right: 40, left: 40, bottom: 20 }}>
            <XAxis
              dataKey="time"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 11, fill: '#6B7280' }}
              interval={0}
            />
            <YAxis
              yAxisId="temp"
              orientation="left"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 11, fill: '#6B7280' }}
              domain={[0, 30]}
              label={{ value: '°C', angle: 0, position: 'insideTopLeft' }}
            />
            <YAxis
              yAxisId="humidity"
              orientation="right"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 11, fill: '#6B7280' }}
              domain={[0, 300]}
              label={{ value: 'J/cm2', angle: 0, position: 'insideTopRight' }}
            />

            {/* Reference lines for temperature */}
            <ReferenceLine y={20} yAxisId="temp" stroke="#38BDF8" strokeWidth={2} strokeDasharray="none" />
            <ReferenceLine y={22} yAxisId="temp" stroke="#38BDF8" strokeWidth={2} strokeDasharray="none" />

            {/* Precipitation bars */}
            <Bar
              yAxisId="temp"
              dataKey="precipitation"
              fill="#22C55E"
              radius={[2, 2, 0, 0]}
              maxBarSize={30}
            />

            {/* Temperature lines */}
            <Line
              yAxisId="temp"
              type="monotone"
              dataKey="temp"
              stroke="#8B5CF6"
              strokeWidth={3}
              dot={false}
            />

            {/* Humidity line */}
            <Line
              yAxisId="humidity"
              type="monotone"
              dataKey="humidity"
              stroke="#EF4444"
              strokeWidth={3}
              dot={false}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};
