'use client';

import React, { useState } from 'react';
import { CARD_CONFIGS, CardConfig } from '../types/dashboard';
import { useDashboardLayout } from '../hooks/useDashboardLayout';

interface DashboardSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export const DashboardSettings: React.FC<DashboardSettingsProps> = ({
  isOpen,
  onClose
}) => {
  const { layout, toggleCardVisibility, resetLayout } = useDashboardLayout();
  const [activeCategory, setActiveCategory] = useState<string>('all');

  if (!isOpen) return null;

  const categories = ['all', 'field', 'weather', 'analytics', 'recommendations', 'controls'];
  
  const getFilteredCards = () => {
    if (activeCategory === 'all') {
      return Object.values(CARD_CONFIGS);
    }
    return Object.values(CARD_CONFIGS).filter(card => card.category === activeCategory);
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      field: '🌾',
      weather: '🌤️',
      analytics: '📊',
      recommendations: '💡',
      controls: '⚙️',
      all: '📋'
    };
    return icons[category as keyof typeof icons] || '📋';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Dashboard Settings</h2>
              <p className="text-blue-100 mt-1">Customize your dashboard layout and visibility</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-blue-200 transition-colors"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Category Filter */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Filter by Category</h3>
            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    activeCategory === category
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {getCategoryIcon(category)} {category.charAt(0).toUpperCase() + category.slice(1)}
                </button>
              ))}
            </div>
          </div>

          {/* Card List */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Card Visibility</h3>
            <div className="space-y-3 max-h-60 overflow-y-auto">
              {getFilteredCards().map(card => (
                <div
                  key={card.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-lg">{getCategoryIcon(card.category)}</span>
                    <div>
                      <h4 className="font-medium text-gray-800">{card.title}</h4>
                      <p className="text-sm text-gray-600">{card.description}</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={layout.cards[card.id]?.visible ?? card.defaultVisible}
                      onChange={() => toggleCardVisibility(card.id)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4 border-t border-gray-200">
            <button
              onClick={resetLayout}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Reset to Default
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors ml-auto"
            >
              Done
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
