'use client';

import { useState, useEffect, useCallback } from 'react';
import { DashboardLayout, CardPosition, CARD_CONFIGS } from '../types/dashboard';

const STORAGE_KEY = 'dashboard-layout';

const getDefaultLayout = (): DashboardLayout => {
  const cards: Record<string, CardPosition> = {};
  
  // Define default positions for each card
  Object.keys(CARD_CONFIGS).forEach((cardId, index) => {
    cards[cardId] = {
      id: cardId,
      x: 0,
      y: 0,
      width: 1,
      height: 1,
      visible: CARD_CONFIGS[cardId].defaultVisible
    };
  });

  return {
    cards,
    gridLayout: [
      'wheat-field',
      'left-field',
      'soybean-field',
      'right-field',
      'weather',
      'weather-metrics',
      'weather-forecast',
      'status-cards',
      'recommendations'
    ]
  };
};

export const useDashboardLayout = () => {
  const [layout, setLayout] = useState<DashboardLayout>(getDefaultLayout());
  const [isLoading, setIsLoading] = useState(true);

  // Load layout from localStorage on mount
  useEffect(() => {
    try {
      const savedLayout = localStorage.getItem(STORAGE_KEY);
      if (savedLayout) {
        const parsedLayout = JSON.parse(savedLayout);

        // Merge with default layout to ensure new cards are included
        const defaultLayout = getDefaultLayout();
        const mergedLayout = {
          ...parsedLayout,
          cards: {
            ...defaultLayout.cards,
            ...parsedLayout.cards
          },
          gridLayout: [
            ...defaultLayout.gridLayout.filter(cardId => !parsedLayout.gridLayout.includes(cardId)),
            ...parsedLayout.gridLayout
          ]
        };

        setLayout(mergedLayout);
      } else {
        setLayout(getDefaultLayout());
      }
    } catch (error) {
      console.error('Failed to load dashboard layout:', error);
      setLayout(getDefaultLayout());
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save layout to localStorage whenever it changes
  const saveLayout = useCallback((newLayout: DashboardLayout) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newLayout));
      setLayout(newLayout);
    } catch (error) {
      console.error('Failed to save dashboard layout:', error);
    }
  }, []);

  // Toggle card visibility
  const toggleCardVisibility = useCallback((cardId: string) => {
    const newLayout = {
      ...layout,
      cards: {
        ...layout.cards,
        [cardId]: {
          ...layout.cards[cardId],
          visible: !layout.cards[cardId].visible
        }
      }
    };
    saveLayout(newLayout);
  }, [layout, saveLayout]);

  // Update card position
  const updateCardPosition = useCallback((cardId: string, position: Partial<CardPosition>) => {
    const newLayout = {
      ...layout,
      cards: {
        ...layout.cards,
        [cardId]: {
          ...layout.cards[cardId],
          ...position
        }
      }
    };
    saveLayout(newLayout);
  }, [layout, saveLayout]);

  // Reorder cards in grid
  const reorderCards = useCallback((newOrder: string[]) => {
    const newLayout = {
      ...layout,
      gridLayout: newOrder
    };
    saveLayout(newLayout);
  }, [layout, saveLayout]);

  // Reset to default layout
  const resetLayout = useCallback(() => {
    const defaultLayout = getDefaultLayout();
    localStorage.removeItem(STORAGE_KEY); // Clear cache
    saveLayout(defaultLayout);
  }, [saveLayout]);

  // Get visible cards
  const getVisibleCards = useCallback(() => {
    return layout.gridLayout.filter(cardId => layout.cards[cardId]?.visible);
  }, [layout]);

  return {
    layout,
    isLoading,
    toggleCardVisibility,
    updateCardPosition,
    reorderCards,
    resetLayout,
    getVisibleCards,
    saveLayout
  };
};
